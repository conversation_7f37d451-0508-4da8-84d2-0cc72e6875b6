package com.tinyzk.service.bwork.converter;

import com.tinyzk.client.bwork.model.vo.AgentCompanySettingVo;
import com.tinyzk.service.bwork.entity.AgentCompanySettingEntity;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T17:26:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class AgentCompanySettingConverterImpl implements AgentCompanySettingConverter {

    @Override
    public AgentCompanySettingVo.EntityVo entityToVo(AgentCompanySettingEntity entity) {
        if ( entity == null ) {
            return null;
        }

        AgentCompanySettingVo.EntityVo entityVo = new AgentCompanySettingVo.EntityVo();

        entityVo.setAgentPlatformDashboardTitle( entity.getAgentPlatformDashboardTitle() );
        entityVo.setAgentPlatformTitle( entity.getAgentPlatformTitle() );
        entityVo.setCompanyId( entity.getCompanyId() );

        return entityVo;
    }
}
