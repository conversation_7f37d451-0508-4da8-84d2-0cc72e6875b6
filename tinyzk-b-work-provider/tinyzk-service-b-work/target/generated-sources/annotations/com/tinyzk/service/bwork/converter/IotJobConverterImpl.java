package com.tinyzk.service.bwork.converter;

import com.tinyzk.client.bwork.model.dto.lot.request.job.IotJobAddExternalFeignRequest;
import com.tinyzk.client.bwork.model.dto.lot.response.IotJobDetailExternalFeignResponse;
import com.tinyzk.client.bwork.model.vo.iot.IotZhiFuBaoSyncJobVO;
import com.tinyzk.service.bwork.entity.lot.LotCompanyJobEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T17:26:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class IotJobConverterImpl implements IotJobConverter {

    @Override
    public IotJobDetailExternalFeignResponse entity2ExternalResponse(LotCompanyJobEntity job) {
        if ( job == null ) {
            return null;
        }

        IotJobDetailExternalFeignResponse.IotJobDetailExternalFeignResponseBuilder iotJobDetailExternalFeignResponse = IotJobDetailExternalFeignResponse.builder();

        iotJobDetailExternalFeignResponse.externalInfo( job.getExtra() );
        iotJobDetailExternalFeignResponse.outId( job.getOutJobId() );
        iotJobDetailExternalFeignResponse.employmentValue( employmentValue( job.getEmploymentCode() ) );
        iotJobDetailExternalFeignResponse.jobRecruitmentValue( jobRecruitmentValue( job.getJobRecruitmentType() ) );
        iotJobDetailExternalFeignResponse.salaryTypeValue( salaryTypeValue( job.getSalaryType() ) );
        iotJobDetailExternalFeignResponse.statusValue( statusValue( job.getStatus() ) );
        iotJobDetailExternalFeignResponse.address( job.getAddress() );
        iotJobDetailExternalFeignResponse.cityCode( job.getCityCode() );
        iotJobDetailExternalFeignResponse.cityName( job.getCityName() );
        iotJobDetailExternalFeignResponse.companyId( job.getCompanyId() );
        iotJobDetailExternalFeignResponse.companyName( job.getCompanyName() );
        iotJobDetailExternalFeignResponse.companyUserId( job.getCompanyUserId() );
        iotJobDetailExternalFeignResponse.companyUserName( job.getCompanyUserName() );
        iotJobDetailExternalFeignResponse.countryCode( job.getCountryCode() );
        iotJobDetailExternalFeignResponse.countryName( job.getCountryName() );
        if ( job.getCreateTime() != null ) {
            iotJobDetailExternalFeignResponse.createTime( LocalDateTime.ofInstant( job.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        iotJobDetailExternalFeignResponse.department( job.getDepartment() );
        iotJobDetailExternalFeignResponse.description( job.getDescription() );
        iotJobDetailExternalFeignResponse.educationalRequirementsCode( job.getEducationalRequirementsCode() );
        iotJobDetailExternalFeignResponse.employmentCode( job.getEmploymentCode() );
        iotJobDetailExternalFeignResponse.expirationEndTime( job.getExpirationEndTime() );
        iotJobDetailExternalFeignResponse.expirationStartTime( job.getExpirationStartTime() );
        iotJobDetailExternalFeignResponse.id( job.getId() );
        iotJobDetailExternalFeignResponse.isAuthBackTone( job.getIsAuthBackTone() );
        iotJobDetailExternalFeignResponse.isNegotiable( job.getIsNegotiable() );
        iotJobDetailExternalFeignResponse.jobName( job.getJobName() );
        iotJobDetailExternalFeignResponse.jobRecruitmentType( job.getJobRecruitmentType() );
        iotJobDetailExternalFeignResponse.jobSkills( job.getJobSkills() );
        iotJobDetailExternalFeignResponse.jobType1Code( job.getJobType1Code() );
        iotJobDetailExternalFeignResponse.jobType2Code( job.getJobType2Code() );
        iotJobDetailExternalFeignResponse.jobType3Code( job.getJobType3Code() );
        if ( job.getLat() != null ) {
            iotJobDetailExternalFeignResponse.lat( job.getLat().floatValue() );
        }
        if ( job.getLng() != null ) {
            iotJobDetailExternalFeignResponse.lng( job.getLng().floatValue() );
        }
        iotJobDetailExternalFeignResponse.monthsSalar( job.getMonthsSalar() );
        iotJobDetailExternalFeignResponse.originSource( job.getOriginSource() );
        iotJobDetailExternalFeignResponse.provinceCode( job.getProvinceCode() );
        iotJobDetailExternalFeignResponse.provinceName( job.getProvinceName() );
        iotJobDetailExternalFeignResponse.recruitingNumbers( job.getRecruitingNumbers() );
        iotJobDetailExternalFeignResponse.recruitmentCompanyId( job.getRecruitmentCompanyId() );
        iotJobDetailExternalFeignResponse.recruitmentCompanyName( job.getRecruitmentCompanyName() );
        if ( job.getSalaryMax() != null ) {
            iotJobDetailExternalFeignResponse.salaryMax( job.getSalaryMax().intValue() );
        }
        if ( job.getSalaryMin() != null ) {
            iotJobDetailExternalFeignResponse.salaryMin( job.getSalaryMin().intValue() );
        }
        iotJobDetailExternalFeignResponse.salaryType( job.getSalaryType() );
        iotJobDetailExternalFeignResponse.status( job.getStatus() );
        iotJobDetailExternalFeignResponse.workExperienceCode( job.getWorkExperienceCode() );

        return iotJobDetailExternalFeignResponse.build();
    }

    @Override
    public List<IotJobDetailExternalFeignResponse> entity2ExternalResponses(List<LotCompanyJobEntity> jobs) {
        if ( jobs == null ) {
            return null;
        }

        List<IotJobDetailExternalFeignResponse> list = new ArrayList<IotJobDetailExternalFeignResponse>( jobs.size() );
        for ( LotCompanyJobEntity lotCompanyJobEntity : jobs ) {
            list.add( entity2ExternalResponse( lotCompanyJobEntity ) );
        }

        return list;
    }

    @Override
    public LotCompanyJobEntity jobAddExternalFeignRequest2Entity(IotJobAddExternalFeignRequest request) {
        if ( request == null ) {
            return null;
        }

        LotCompanyJobEntity.LotCompanyJobEntityBuilder lotCompanyJobEntity = LotCompanyJobEntity.builder();

        lotCompanyJobEntity.extra( request.getExternalInfo() );
        lotCompanyJobEntity.outJobId( request.getOutId() );
        lotCompanyJobEntity.salaryMonth( request.getSalaryType() );
        lotCompanyJobEntity.address( request.getAddress() );
        lotCompanyJobEntity.cityCode( request.getCityCode() );
        lotCompanyJobEntity.cityName( request.getCityName() );
        lotCompanyJobEntity.companyId( request.getCompanyId() );
        lotCompanyJobEntity.companyName( request.getCompanyName() );
        lotCompanyJobEntity.companyUserId( request.getCompanyUserId() );
        lotCompanyJobEntity.companyUserName( request.getCompanyUserName() );
        lotCompanyJobEntity.countryCode( request.getCountryCode() );
        lotCompanyJobEntity.countryName( request.getCountryName() );
        lotCompanyJobEntity.department( request.getDepartment() );
        lotCompanyJobEntity.description( request.getDescription() );
        lotCompanyJobEntity.educationalRequirementsCode( request.getEducationalRequirementsCode() );
        lotCompanyJobEntity.employmentCode( request.getEmploymentCode() );
        lotCompanyJobEntity.expirationEndTime( request.getExpirationEndTime() );
        lotCompanyJobEntity.expirationStartTime( request.getExpirationStartTime() );
        lotCompanyJobEntity.isAuthBackTone( request.getIsAuthBackTone() );
        lotCompanyJobEntity.isNegotiable( request.getIsNegotiable() );
        lotCompanyJobEntity.jobName( request.getJobName() );
        lotCompanyJobEntity.jobRecruitmentType( request.getJobRecruitmentType() );
        lotCompanyJobEntity.jobSkills( request.getJobSkills() );
        lotCompanyJobEntity.jobType1Code( request.getJobType1Code() );
        lotCompanyJobEntity.jobType2Code( request.getJobType2Code() );
        lotCompanyJobEntity.jobType3Code( request.getJobType3Code() );
        if ( request.getLat() != null ) {
            lotCompanyJobEntity.lat( BigDecimal.valueOf( request.getLat() ) );
        }
        if ( request.getLng() != null ) {
            lotCompanyJobEntity.lng( BigDecimal.valueOf( request.getLng() ) );
        }
        lotCompanyJobEntity.monthsSalar( request.getMonthsSalar() );
        lotCompanyJobEntity.originSource( request.getOriginSource() );
        lotCompanyJobEntity.provinceCode( request.getProvinceCode() );
        lotCompanyJobEntity.provinceName( request.getProvinceName() );
        lotCompanyJobEntity.recruitingNumbers( request.getRecruitingNumbers() );
        lotCompanyJobEntity.recruitmentCompanyId( request.getRecruitmentCompanyId() );
        lotCompanyJobEntity.recruitmentCompanyName( request.getRecruitmentCompanyName() );
        if ( request.getSalaryMax() != null ) {
            lotCompanyJobEntity.salaryMax( BigDecimal.valueOf( request.getSalaryMax() ) );
        }
        if ( request.getSalaryMin() != null ) {
            lotCompanyJobEntity.salaryMin( BigDecimal.valueOf( request.getSalaryMin() ) );
        }
        lotCompanyJobEntity.salaryType( request.getSalaryType() );
        lotCompanyJobEntity.source( request.getSource() );
        lotCompanyJobEntity.status( request.getStatus() );
        lotCompanyJobEntity.workExperienceCode( request.getWorkExperienceCode() );

        return lotCompanyJobEntity.build();
    }

    @Override
    public List<LotCompanyJobEntity> jobAddExternalFeignRequest2Entities(List<IotJobAddExternalFeignRequest> request) {
        if ( request == null ) {
            return null;
        }

        List<LotCompanyJobEntity> list = new ArrayList<LotCompanyJobEntity>( request.size() );
        for ( IotJobAddExternalFeignRequest iotJobAddExternalFeignRequest : request ) {
            list.add( jobAddExternalFeignRequest2Entity( iotJobAddExternalFeignRequest ) );
        }

        return list;
    }

    @Override
    public IotZhiFuBaoSyncJobVO entity2ZhiFuBaoResponse(LotCompanyJobEntity job) {
        if ( job == null ) {
            return null;
        }

        IotZhiFuBaoSyncJobVO iotZhiFuBaoSyncJobVO = new IotZhiFuBaoSyncJobVO();

        if ( job.getId() != null ) {
            iotZhiFuBaoSyncJobVO.setOut_job_id( String.valueOf( job.getId() ) );
        }
        iotZhiFuBaoSyncJobVO.setJob_name( job.getJobName() );
        iotZhiFuBaoSyncJobVO.setAddress( address( job ) );
        iotZhiFuBaoSyncJobVO.setEmployer_name( job.getCompanyName() );
        iotZhiFuBaoSyncJobVO.setHire_status( hireStatus( job.getStatus() ) );
        iotZhiFuBaoSyncJobVO.setPart_time_mode( partTimeMode( job ) );
        iotZhiFuBaoSyncJobVO.setSalary( salary( job ) );
        iotZhiFuBaoSyncJobVO.setWorking_years( workingYears( job.getWorkExperienceCode() ) );
        iotZhiFuBaoSyncJobVO.setAcademic_require( academicRequire( job.getEducationalRequirementsCode() ) );
        iotZhiFuBaoSyncJobVO.setJob_detail( job.getDescription() );
        iotZhiFuBaoSyncJobVO.setJob_type( job.getJob_type() );

        iotZhiFuBaoSyncJobVO.setWork_online( Boolean.FALSE );
        iotZhiFuBaoSyncJobVO.setJob_detail_url( cn.hutool.core.util.StrUtil.concat(false,"alipays://platformapi/startapp?appId=2021002134692569&page=pages/iot/iot-detail-yingpinl%3FjobId%3D",job.getId().toString()) );
        iotZhiFuBaoSyncJobVO.setJob_worth_req( "NO_REQ" );
        iotZhiFuBaoSyncJobVO.setEmployer_type( "COMPANY" );
        iotZhiFuBaoSyncJobVO.setStart_date( new java.text.SimpleDateFormat("yyyyMMdd").format(job.getCreateTime()) );
        iotZhiFuBaoSyncJobVO.setExpired_date( "20301231" );
        iotZhiFuBaoSyncJobVO.setAge( "不限,不限" );
        iotZhiFuBaoSyncJobVO.setGender( "ALL" );
        iotZhiFuBaoSyncJobVO.setPay_period( "月结" );
        iotZhiFuBaoSyncJobVO.setCount( job.getRecruitingNumbers() == null ? 1 : job.getRecruitingNumbers() );
        iotZhiFuBaoSyncJobVO.setIot_status( 1 );

        return iotZhiFuBaoSyncJobVO;
    }

    @Override
    public List<IotZhiFuBaoSyncJobVO> entity2ZhiFuBaoResponses(List<LotCompanyJobEntity> jobs) {
        if ( jobs == null ) {
            return null;
        }

        List<IotZhiFuBaoSyncJobVO> list = new ArrayList<IotZhiFuBaoSyncJobVO>( jobs.size() );
        for ( LotCompanyJobEntity lotCompanyJobEntity : jobs ) {
            list.add( entity2ZhiFuBaoResponse( lotCompanyJobEntity ) );
        }

        return list;
    }
}
