package com.tinyzk.service.bwork.converter;

import com.tinyzk.client.bwork.model.dto.lot.request.job.IotSchoolJobAddExternalFeignRequest;
import com.tinyzk.client.bwork.model.dto.lot.response.IotSchoolJobDetailExternalFeignResponse;
import com.tinyzk.service.bwork.entity.lot.LotCompanyJobEntity;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T17:26:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class IotSchoolJobConverterImpl implements IotSchoolJobConverter {

    @Override
    public IotSchoolJobDetailExternalFeignResponse entity2ExternalResponse(LotCompanyJobEntity job) {
        if ( job == null ) {
            return null;
        }

        IotSchoolJobDetailExternalFeignResponse.IotSchoolJobDetailExternalFeignResponseBuilder iotSchoolJobDetailExternalFeignResponse = IotSchoolJobDetailExternalFeignResponse.builder();

        iotSchoolJobDetailExternalFeignResponse.externalInfo( job.getExtra() );
        iotSchoolJobDetailExternalFeignResponse.outId( job.getOutJobId() );
        iotSchoolJobDetailExternalFeignResponse.salaryTypeValue( salaryTypeValue( job.getSalaryType() ) );
        iotSchoolJobDetailExternalFeignResponse.statusValue( statusValue( job.getStatus() ) );
        iotSchoolJobDetailExternalFeignResponse.schoolId( job.getSchoolGrade() );
        iotSchoolJobDetailExternalFeignResponse.subjectId( job.getSchoolSubject() );
        iotSchoolJobDetailExternalFeignResponse.address( job.getAddress() );
        iotSchoolJobDetailExternalFeignResponse.cityCode( job.getCityCode() );
        iotSchoolJobDetailExternalFeignResponse.cityName( job.getCityName() );
        iotSchoolJobDetailExternalFeignResponse.companyId( job.getCompanyId() );
        iotSchoolJobDetailExternalFeignResponse.companyName( job.getCompanyName() );
        iotSchoolJobDetailExternalFeignResponse.companyUserId( job.getCompanyUserId() );
        iotSchoolJobDetailExternalFeignResponse.companyUserName( job.getCompanyUserName() );
        iotSchoolJobDetailExternalFeignResponse.countryCode( job.getCountryCode() );
        iotSchoolJobDetailExternalFeignResponse.countryName( job.getCountryName() );
        if ( job.getCreateTime() != null ) {
            iotSchoolJobDetailExternalFeignResponse.createTime( LocalDateTime.ofInstant( job.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        iotSchoolJobDetailExternalFeignResponse.description( job.getDescription() );
        iotSchoolJobDetailExternalFeignResponse.educationalRequirementsCode( job.getEducationalRequirementsCode() );
        iotSchoolJobDetailExternalFeignResponse.expirationEndTime( job.getExpirationEndTime() );
        iotSchoolJobDetailExternalFeignResponse.expirationStartTime( job.getExpirationStartTime() );
        iotSchoolJobDetailExternalFeignResponse.id( job.getId() );
        iotSchoolJobDetailExternalFeignResponse.isAuthBackTone( job.getIsAuthBackTone() );
        iotSchoolJobDetailExternalFeignResponse.jobName( job.getJobName() );
        iotSchoolJobDetailExternalFeignResponse.jobSkills( job.getJobSkills() );
        iotSchoolJobDetailExternalFeignResponse.jobType1Code( job.getJobType1Code() );
        iotSchoolJobDetailExternalFeignResponse.jobType2Code( job.getJobType2Code() );
        iotSchoolJobDetailExternalFeignResponse.jobType3Code( job.getJobType3Code() );
        if ( job.getLat() != null ) {
            iotSchoolJobDetailExternalFeignResponse.lat( job.getLat().floatValue() );
        }
        if ( job.getLng() != null ) {
            iotSchoolJobDetailExternalFeignResponse.lng( job.getLng().floatValue() );
        }
        iotSchoolJobDetailExternalFeignResponse.provinceCode( job.getProvinceCode() );
        iotSchoolJobDetailExternalFeignResponse.provinceName( job.getProvinceName() );
        iotSchoolJobDetailExternalFeignResponse.recruitingNumbers( job.getRecruitingNumbers() );
        iotSchoolJobDetailExternalFeignResponse.recruitmentCompanyId( job.getRecruitmentCompanyId() );
        iotSchoolJobDetailExternalFeignResponse.recruitmentCompanyName( job.getRecruitmentCompanyName() );
        if ( job.getSalaryMax() != null ) {
            iotSchoolJobDetailExternalFeignResponse.salaryMax( job.getSalaryMax().intValue() );
        }
        if ( job.getSalaryMin() != null ) {
            iotSchoolJobDetailExternalFeignResponse.salaryMin( job.getSalaryMin().intValue() );
        }
        iotSchoolJobDetailExternalFeignResponse.salaryType( job.getSalaryType() );
        iotSchoolJobDetailExternalFeignResponse.status( job.getStatus() );
        iotSchoolJobDetailExternalFeignResponse.workExperienceCode( job.getWorkExperienceCode() );

        return iotSchoolJobDetailExternalFeignResponse.build();
    }

    @Override
    public List<IotSchoolJobDetailExternalFeignResponse> entity2ExternalResponses(List<LotCompanyJobEntity> jobs) {
        if ( jobs == null ) {
            return null;
        }

        List<IotSchoolJobDetailExternalFeignResponse> list = new ArrayList<IotSchoolJobDetailExternalFeignResponse>( jobs.size() );
        for ( LotCompanyJobEntity lotCompanyJobEntity : jobs ) {
            list.add( entity2ExternalResponse( lotCompanyJobEntity ) );
        }

        return list;
    }

    @Override
    public LotCompanyJobEntity jobAddExternalFeignRequest2Entity(IotSchoolJobAddExternalFeignRequest request) {
        if ( request == null ) {
            return null;
        }

        LotCompanyJobEntity.LotCompanyJobEntityBuilder lotCompanyJobEntity = LotCompanyJobEntity.builder();

        lotCompanyJobEntity.extra( request.getExternalInfo() );
        lotCompanyJobEntity.outJobId( request.getOutId() );
        lotCompanyJobEntity.salaryMonth( request.getSalaryType() );
        lotCompanyJobEntity.address( request.getAddress() );
        lotCompanyJobEntity.cityCode( request.getCityCode() );
        lotCompanyJobEntity.cityName( request.getCityName() );
        lotCompanyJobEntity.companyId( request.getCompanyId() );
        lotCompanyJobEntity.companyName( request.getCompanyName() );
        lotCompanyJobEntity.companyUserId( request.getCompanyUserId() );
        lotCompanyJobEntity.companyUserName( request.getCompanyUserName() );
        lotCompanyJobEntity.countryCode( request.getCountryCode() );
        lotCompanyJobEntity.countryName( request.getCountryName() );
        lotCompanyJobEntity.description( request.getDescription() );
        lotCompanyJobEntity.educationalRequirementsCode( request.getEducationalRequirementsCode() );
        lotCompanyJobEntity.expirationEndTime( request.getExpirationEndTime() );
        lotCompanyJobEntity.expirationStartTime( request.getExpirationStartTime() );
        lotCompanyJobEntity.isAuthBackTone( request.getIsAuthBackTone() );
        lotCompanyJobEntity.jobName( request.getJobName() );
        lotCompanyJobEntity.jobSkills( request.getJobSkills() );
        lotCompanyJobEntity.jobType1Code( request.getJobType1Code() );
        lotCompanyJobEntity.jobType2Code( request.getJobType2Code() );
        lotCompanyJobEntity.jobType3Code( request.getJobType3Code() );
        if ( request.getLat() != null ) {
            lotCompanyJobEntity.lat( BigDecimal.valueOf( request.getLat() ) );
        }
        if ( request.getLng() != null ) {
            lotCompanyJobEntity.lng( BigDecimal.valueOf( request.getLng() ) );
        }
        lotCompanyJobEntity.provinceCode( request.getProvinceCode() );
        lotCompanyJobEntity.provinceName( request.getProvinceName() );
        lotCompanyJobEntity.recruitingNumbers( request.getRecruitingNumbers() );
        lotCompanyJobEntity.recruitmentCompanyId( request.getRecruitmentCompanyId() );
        lotCompanyJobEntity.recruitmentCompanyName( request.getRecruitmentCompanyName() );
        if ( request.getSalaryMax() != null ) {
            lotCompanyJobEntity.salaryMax( BigDecimal.valueOf( request.getSalaryMax() ) );
        }
        if ( request.getSalaryMin() != null ) {
            lotCompanyJobEntity.salaryMin( BigDecimal.valueOf( request.getSalaryMin() ) );
        }
        lotCompanyJobEntity.salaryType( request.getSalaryType() );
        lotCompanyJobEntity.status( request.getStatus() );
        lotCompanyJobEntity.workExperienceCode( request.getWorkExperienceCode() );

        lotCompanyJobEntity.jobRecruitmentType( com.tinyzk.client.bwork.model.enums.JobRecruitmentTypeEnum.CAMPUS_RECRUITMENT.getCode() );
        lotCompanyJobEntity.isNegotiable( Boolean.FALSE );
        lotCompanyJobEntity.schoolGrade( org.apache.commons.lang3.StringUtils.join(request.getSchoolId(),",") );
        lotCompanyJobEntity.schoolSubject( org.apache.commons.lang3.StringUtils.join(request.getSchoolId(),",") );
        lotCompanyJobEntity.proxyRecruitmentType( 2 );

        return lotCompanyJobEntity.build();
    }

    @Override
    public List<LotCompanyJobEntity> jobAddExternalFeignRequest2Entities(List<IotSchoolJobAddExternalFeignRequest> request) {
        if ( request == null ) {
            return null;
        }

        List<LotCompanyJobEntity> list = new ArrayList<LotCompanyJobEntity>( request.size() );
        for ( IotSchoolJobAddExternalFeignRequest iotSchoolJobAddExternalFeignRequest : request ) {
            list.add( jobAddExternalFeignRequest2Entity( iotSchoolJobAddExternalFeignRequest ) );
        }

        return list;
    }
}
