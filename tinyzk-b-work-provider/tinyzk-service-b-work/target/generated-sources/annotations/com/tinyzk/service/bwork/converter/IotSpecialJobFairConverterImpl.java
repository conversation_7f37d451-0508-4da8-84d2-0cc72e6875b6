package com.tinyzk.service.bwork.converter;

import com.tinyzk.client.bwork.model.dto.lot.request.specialJobFair.IotSpecialJobFairAddExternalFeignRequest;
import com.tinyzk.client.bwork.model.dto.lot.response.IotSpecialJobFairDetailExternalFeignResponse;
import com.tinyzk.service.bwork.entity.lot.IotSpecialJobFairEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T17:26:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class IotSpecialJobFairConverterImpl implements IotSpecialJobFairConverter {

    @Override
    public IotSpecialJobFairEntity iotSpecialJobFairAddExternalRequest2Entity(IotSpecialJobFairAddExternalFeignRequest request) {
        if ( request == null ) {
            return null;
        }

        IotSpecialJobFairEntity iotSpecialJobFairEntity = new IotSpecialJobFairEntity();

        iotSpecialJobFairEntity.setPosition( request.getOrder() );
        iotSpecialJobFairEntity.setBannerLink( request.getBannerLink() );
        iotSpecialJobFairEntity.setCompanyId( request.getCompanyId() );
        iotSpecialJobFairEntity.setExternalInfo( request.getExternalInfo() );
        iotSpecialJobFairEntity.setOutId( request.getOutId() );
        iotSpecialJobFairEntity.setSpecialJobFairName( request.getSpecialJobFairName() );

        return iotSpecialJobFairEntity;
    }

    @Override
    public IotSpecialJobFairDetailExternalFeignResponse entity2IotSpecialJobFairExternalResponse(IotSpecialJobFairEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IotSpecialJobFairDetailExternalFeignResponse.IotSpecialJobFairDetailExternalFeignResponseBuilder iotSpecialJobFairDetailExternalFeignResponse = IotSpecialJobFairDetailExternalFeignResponse.builder();

        iotSpecialJobFairDetailExternalFeignResponse.bannerLink( entity.getBannerLink() );
        iotSpecialJobFairDetailExternalFeignResponse.companyId( entity.getCompanyId() );
        iotSpecialJobFairDetailExternalFeignResponse.createTime( entity.getCreateTime() );
        iotSpecialJobFairDetailExternalFeignResponse.externalInfo( entity.getExternalInfo() );
        iotSpecialJobFairDetailExternalFeignResponse.outId( entity.getOutId() );
        iotSpecialJobFairDetailExternalFeignResponse.specialJobFairId( entity.getSpecialJobFairId() );
        iotSpecialJobFairDetailExternalFeignResponse.specialJobFairName( entity.getSpecialJobFairName() );

        return iotSpecialJobFairDetailExternalFeignResponse.build();
    }

    @Override
    public List<IotSpecialJobFairDetailExternalFeignResponse> entity2IotSpecialJobFairExternalResponses(List<IotSpecialJobFairEntity> fairs) {
        if ( fairs == null ) {
            return null;
        }

        List<IotSpecialJobFairDetailExternalFeignResponse> list = new ArrayList<IotSpecialJobFairDetailExternalFeignResponse>( fairs.size() );
        for ( IotSpecialJobFairEntity iotSpecialJobFairEntity : fairs ) {
            list.add( entity2IotSpecialJobFairExternalResponse( iotSpecialJobFairEntity ) );
        }

        return list;
    }
}
