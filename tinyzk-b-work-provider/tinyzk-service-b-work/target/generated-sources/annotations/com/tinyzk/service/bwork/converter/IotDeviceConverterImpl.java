package com.tinyzk.service.bwork.converter;

import com.tinyzk.client.bwork.model.dto.lot.response.IotDeviceDetailExternalFeignResponse;
import com.tinyzk.client.bwork.model.dto.lot.response.IotDeviceOnlineDetailExternalFeignResponse;
import com.tinyzk.service.bwork.dto.IotDeviceOnlineDTO;
import com.tinyzk.service.bwork.entity.IotDeviceEntity;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-20T17:26:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
public class IotDeviceConverterImpl implements IotDeviceConverter {

    @Override
    public IotDeviceDetailExternalFeignResponse entity2ExternalResponse(IotDeviceEntity device) {
        if ( device == null ) {
            return null;
        }

        IotDeviceDetailExternalFeignResponse.IotDeviceDetailExternalFeignResponseBuilder iotDeviceDetailExternalFeignResponse = IotDeviceDetailExternalFeignResponse.builder();

        iotDeviceDetailExternalFeignResponse.deviceCode( device.getCode() );
        iotDeviceDetailExternalFeignResponse.deviceName( device.getIotName() );
        iotDeviceDetailExternalFeignResponse.deviceTypeCode( device.getDeviceType() );
        iotDeviceDetailExternalFeignResponse.deviceTypeValue( deviceCode2Value( device.getDeviceType() ) );
        iotDeviceDetailExternalFeignResponse.agentId( device.getAgentId() );
        iotDeviceDetailExternalFeignResponse.agentName( device.getAgentName() );
        iotDeviceDetailExternalFeignResponse.createTime( device.getCreateTime() );
        iotDeviceDetailExternalFeignResponse.externalInfo( device.getExternalInfo() );
        iotDeviceDetailExternalFeignResponse.id( device.getId() );
        iotDeviceDetailExternalFeignResponse.outId( device.getOutId() );

        return iotDeviceDetailExternalFeignResponse.build();
    }

    @Override
    public List<IotDeviceDetailExternalFeignResponse> entity2ExternalResponses(List<IotDeviceEntity> devices) {
        if ( devices == null ) {
            return null;
        }

        List<IotDeviceDetailExternalFeignResponse> list = new ArrayList<IotDeviceDetailExternalFeignResponse>( devices.size() );
        for ( IotDeviceEntity iotDeviceEntity : devices ) {
            list.add( entity2ExternalResponse( iotDeviceEntity ) );
        }

        return list;
    }

    @Override
    public IotDeviceOnlineDetailExternalFeignResponse deviceOnlineDTO2IotDeviceOnlineDetailExternalResponse(IotDeviceOnlineDTO device) {
        if ( device == null ) {
            return null;
        }

        IotDeviceOnlineDetailExternalFeignResponse.IotDeviceOnlineDetailExternalFeignResponseBuilder iotDeviceOnlineDetailExternalFeignResponse = IotDeviceOnlineDetailExternalFeignResponse.builder();

        iotDeviceOnlineDetailExternalFeignResponse.deviceTypeCode( device.getDeviceType() );
        iotDeviceOnlineDetailExternalFeignResponse.deviceTypeValue( deviceCode2Value( device.getDeviceType() ) );
        iotDeviceOnlineDetailExternalFeignResponse.deviceName( device.getIotName() );
        iotDeviceOnlineDetailExternalFeignResponse.deviceCode( device.getCode() );
        iotDeviceOnlineDetailExternalFeignResponse.agentId( device.getAgentId() );
        iotDeviceOnlineDetailExternalFeignResponse.agentName( device.getAgentName() );
        iotDeviceOnlineDetailExternalFeignResponse.createTime( device.getCreateTime() );
        iotDeviceOnlineDetailExternalFeignResponse.externalInfo( device.getExternalInfo() );
        iotDeviceOnlineDetailExternalFeignResponse.id( device.getId() );
        iotDeviceOnlineDetailExternalFeignResponse.isOnline( device.getIsOnline() );
        iotDeviceOnlineDetailExternalFeignResponse.outId( device.getOutId() );

        return iotDeviceOnlineDetailExternalFeignResponse.build();
    }

    @Override
    public List<IotDeviceOnlineDetailExternalFeignResponse> deviceOnlineDTO2IotDeviceOnlineDetailExternalResponses(List<IotDeviceOnlineDTO> devices) {
        if ( devices == null ) {
            return null;
        }

        List<IotDeviceOnlineDetailExternalFeignResponse> list = new ArrayList<IotDeviceOnlineDetailExternalFeignResponse>( devices.size() );
        for ( IotDeviceOnlineDTO iotDeviceOnlineDTO : devices ) {
            list.add( deviceOnlineDTO2IotDeviceOnlineDetailExternalResponse( iotDeviceOnlineDTO ) );
        }

        return list;
    }
}
