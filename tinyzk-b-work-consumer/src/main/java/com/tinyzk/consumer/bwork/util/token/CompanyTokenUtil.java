package com.tinyzk.consumer.bwork.util.token;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alipay.api.domain.UserInfoVO;
import com.tinyzk.common.constant.RedisConstants;
import com.tinyzk.common.core.redis.RedisUtil;
import com.tinyzk.common.exception.GlobalException;
import com.tinyzk.common.user.UserDetailUtils;
import com.tinyzk.common.user.model.UserDetail;
import com.tinyzk.common.utils.ServletUtils;
import com.tinyzk.consumer.bwork.conf.ApplicationConfig;
import com.tinyzk.consumer.bwork.request.CompanyUserLoginInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;

/**
 * @ProjectName: tinyzk-comm-provider
 * @Package: com.tinyzk.consumer.bwork.util.token
 * @ClassName: CompanyTokenUtil
 * @Author: Administrator
 * @Description: B端用户token帮助类
 * @Date: 2021/1/12 0012 18:28
 * @Version: 1.0
 */
@Component
@Slf4j
public class CompanyTokenUtil extends ServletUtils<CompanyUserLoginInfoDTO> {

    @Autowired
    private ApplicationConfig applicationConfig;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public CompanyUserLoginInfoDTO getTokenInfo() {

        UserDetail userDetail = UserDetailUtils.get();
        CompanyUserLoginInfoDTO companyUserLoginInfoDTO= CompanyUserLoginInfoDTO.builder().build();
        BeanUtils.copyProperties(userDetail,companyUserLoginInfoDTO);
        return companyUserLoginInfoDTO;
    }

    public void deleteToken(Long userId, String tokenKeyFix) {
        String getUserTokenKey = tokenKeyFix + userId;
        redisUtil.delete(getUserTokenKey);
        String tokenKey = redisUtil.get(getUserTokenKey);
        redisUtil.delete(tokenKeyFix + tokenKey);
    }

    public void disable2UpdateUserTokenInfo(@NotNull Long userId, UserDetail managerUser, String tokenKeyFix) {
        String getUserTokenKey = tokenKeyFix + userId;
        String token = redisUtil.get(getUserTokenKey);
        if (StringUtils.isBlank(token)) {
            throw new GlobalException(401, "请重新登录");
        }
        String tokenKey = tokenKeyFix + token;
        String userJson = redisUtil.get(tokenKey);
        UserDetail userDetail = JSONObject.parseObject(userJson, UserDetail.class);
        userDetail.setStatus(false);
        userDetail.setDisableUserId(managerUser.getUserId());
        userDetail.setDisableUserName(managerUser.getUserName());
        redisUtil.set(tokenKey, JSON.toJSONString(userDetail), RedisConstants.EXPIRE);

    }

    /**
     * 日志专用  其他地方不要使用此方法
     *
     * @return
     */
    public void getTokenInfoByLog() {
        String headToken = getRequest().getHeader(applicationConfig.getHeadToken());
        if (StrUtil.isNotBlank(headToken)) {
            log.info("获取Token信息-->{}", headToken);
            String token = RedisConstants.B_WORK_TOKEN_KEY;
            token = token + headToken;
            if (!redisUtil.hasKey(token)) {
                log.info("Redis中Token信息不存在,非登录状态.....");
            }
            String str = redisUtil.get(token);
            log.info("解析token,获得用户详细信息-->{}", str);
        }
    }
}
